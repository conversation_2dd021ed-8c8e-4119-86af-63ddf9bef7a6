mod config;
mod bot;

use anyhow::{Context, Result};
use bot::TradeBot;
use config::BotConfig;
use solana_sdk::signature::Signer;
use std::env;
use std::path::Path;

#[tokio::main]
async fn main() -> Result<()> {
    // Get config file path from environment variable or use default
    let config_path = env::var("CONFIG_PATH").unwrap_or_else(|_| "bot/config.json".to_string());
    println!("Loading config from: {}", config_path);

    // Get private keys file path from environment variable or use default
    let private_keys_path = env::var("PRIVATE_KEYS_PATH").unwrap_or_else(|_| "bot/private_keys.json".to_string());
    println!("Loading private keys from: {}", private_keys_path);

    // Load configuration with private keys
    let config = BotConfig::from_file_with_private_keys(
        Path::new(&config_path),
        Path::new(&private_keys_path)
    ).context("Failed to load configuration and private keys")?;

    // Create trade bot
    let bot = TradeBot::new(config);

    // Execute swaps for all wallets
    println!("Starting swaps for {} wallets...", bot.config.wallets.len());
    let results = bot.execute_swaps().await;

    // Print results
    for (i, result) in results.iter().enumerate() {
        // Get wallet address for this result
        let wallet_address = if let Some(wallet) = bot.config.wallets.get(i) {
            // Load keypair to get the public key
            match TradeBot::load_keypair_for_address(&wallet.private_key) {
                Ok(keypair) => keypair.pubkey().to_string(),
                Err(_) => format!("Wallet {}", i + 1),
            }
        } else {
            format!("Wallet {}", i + 1)
        };

        match result {
            Ok(signature) => println!("{}: Swap successful! Signature: {}", wallet_address, signature),
            Err(e) => println!("{}: Swap failed: {}", wallet_address, e),
        }
    }
    Ok(())
}
