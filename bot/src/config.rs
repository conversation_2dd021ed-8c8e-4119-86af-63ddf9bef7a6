use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::fs;
use std::path::Path;
use std::str::FromStr;

/// Default minimum percentage of wallet balance to use for swap
fn default_min_percentage() -> u8 {
    90
}

/// Default maximum percentage of wallet balance to use for swap
fn default_max_percentage() -> u8 {
    95
}

/// Default maximum concurrent swaps
fn default_max_concurrent_swaps() -> usize {
    3
}

/// Default maximum concurrent balance fetches
fn default_max_concurrent_balance_fetches() -> usize {
    10
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WalletConfig {
    /// Base58 encoded private key
    pub private_key: String,
    /// Wallet balance in lamports (not serialized/deserialized)
    #[serde(skip)]
    pub balance: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenConfig {
    /// Token mint address
    pub mint_address: String,
    /// Amount to swap (in token's smallest unit)
    pub amount: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SwapConfig {
    /// Input token configuration
    pub input_token: TokenConfig,
    /// Output token configuration
    pub output_token: TokenConfig,
    /// Slippage in basis points (1 bps = 0.01%)
    pub slippage_bps: u16,
    /// Minimum percentage of wallet balance to use for swap
    #[serde(default = "default_min_percentage")]
    pub min_percentage: u8,
    /// Maximum percentage of wallet balance to use for swap
    #[serde(default = "default_max_percentage")]
    pub max_percentage: u8,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConcurrencyConfig {
    /// Maximum number of concurrent swap operations (default: 3)
    #[serde(default = "default_max_concurrent_swaps")]
    pub max_concurrent_swaps: usize,
    /// Maximum number of concurrent balance fetch operations (default: 10)
    #[serde(default = "default_max_concurrent_balance_fetches")]
    pub max_concurrent_balance_fetches: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BotConfig {
    /// List of wallets to use for trading
    #[serde(default)]
    pub wallets: Vec<WalletConfig>,
    /// Swap configuration
    pub swap: SwapConfig,
    /// Concurrency configuration
    #[serde(default)]
    pub concurrency: Option<ConcurrencyConfig>,
    /// Jupiter API base URL
    pub api_base_url: Option<String>,
    /// RPC endpoint URL
    pub rpc_url: Option<String>,
}

/// Load private keys from a separate file
pub fn load_private_keys<P: AsRef<Path>>(path: P) -> Result<Vec<String>> {
    let keys_str = fs::read_to_string(path)
        .context("Failed to read private keys file")?;

    let keys: Vec<String> = serde_json::from_str(&keys_str)
        .context("Failed to parse private keys file")?;

    Ok(keys)
}

impl BotConfig {
    /// Load configuration from a file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let config_str = fs::read_to_string(path)
            .context("Failed to read config file")?;

        let config: BotConfig = serde_json::from_str(&config_str)
            .context("Failed to parse config file")?;

        Ok(config)
    }

    /// Load configuration from a file and private keys from a separate file
    pub fn from_file_with_private_keys<P1: AsRef<Path>, P2: AsRef<Path>>(config_path: P1, private_keys_path: P2) -> Result<Self> {
        // Load main configuration
        let mut config = Self::from_file(config_path)?;

        // Load private keys
        let private_keys = load_private_keys(private_keys_path)?;

        // Create wallet configs from private keys
        config.wallets = private_keys.into_iter()
            .map(|private_key| WalletConfig {
                private_key,
                balance: None
            })
            .collect();

        Ok(config)
    }

    /// Get the input token mint as Pubkey
    pub fn input_mint(&self) -> Result<Pubkey> {
        Pubkey::from_str(&self.swap.input_token.mint_address)
            .context("Invalid input token mint address")
    }

    /// Get the output token mint as Pubkey
    pub fn output_mint(&self) -> Result<Pubkey> {
        Pubkey::from_str(&self.swap.output_token.mint_address)
            .context("Invalid output token mint address")
    }

    /// Get the API base URL or default
    pub fn api_base_url(&self) -> String {
        self.api_base_url.clone().unwrap_or_else(|| "https://quote-api.jup.ag/v6".to_string())
    }

    /// Get the RPC URL or default
    pub fn rpc_url(&self) -> String {
        self.rpc_url.clone().unwrap_or_else(|| "https://api.mainnet-beta.solana.com".to_string())
    }

    /// Get the maximum concurrent swaps or default
    pub fn max_concurrent_swaps(&self) -> usize {
        self.concurrency.as_ref()
            .map(|c| c.max_concurrent_swaps)
            .unwrap_or_else(default_max_concurrent_swaps)
    }

    /// Get the maximum concurrent balance fetches or default
    pub fn max_concurrent_balance_fetches(&self) -> usize {
        self.concurrency.as_ref()
            .map(|c| c.max_concurrent_balance_fetches)
            .unwrap_or_else(default_max_concurrent_balance_fetches)
    }
}
