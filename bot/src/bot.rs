use crate::config::BotConfig;
use anyhow::{Context, Result};
use jupiter_swap_api_client::{
    quote::QuoteRequest, swap::SwapRequest, transaction_config::TransactionConfig,
    JupiterSwapApiClient,
};
use rand;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::{
    bs58
    ,
    signature::{Keypair, Signer},
    transaction::VersionedTransaction,
};
use std::sync::Arc;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;

pub struct TradeBot {
    pub config: BotConfig,
    jupiter_client: JupiterSwapApiClient,
    rpc_client: Arc<RpcClient>,
}

impl TradeBot {
    /// Create a new trade bot with the given configuration
    pub fn new(config: BotConfig) -> Self {
        let jupiter_client = JupiterSwapApiClient::new(config.api_base_url());
        let rpc_client = Arc::new(RpcClient::new(config.rpc_url()));

        Self {
            config,
            jupiter_client,
            rpc_client,
        }
    }

    /// Load keypair from base58 encoded private key
    fn load_keypair(private_key: &str) -> Result<Keypair> {
        let keypair_bytes = bs58::decode(private_key)
            .into_vec()
            .context("Failed to decode private key")?;

        Keypair::from_bytes(&keypair_bytes)
            .context("Invalid keypair bytes")
    }

    /// Load wallet and fetch its balance
    async fn load_wallet_with_balance(&self, wallet: &mut crate::config::WalletConfig) -> Result<()> {
        // Load the keypair
        let keypair = Self::load_keypair(&wallet.private_key)?;
        let pubkey = keypair.pubkey();

        // Get the wallet's actual SOL balance
        let balance = self.rpc_client.get_balance(&pubkey).await
            .context("Failed to get wallet balance")?;

        // Store the balance in the wallet config
        wallet.balance = Some(balance);

        println!("Loaded wallet {} with balance: {} lamports", pubkey, balance);

        Ok(())
    }

    /// Execute a swap for a single wallet (legacy method, kept for compatibility)
    async fn execute_swap_for_wallet(&self, wallet_private_key: &str) -> Result<String> {
        // Load the keypair
        let signer = Self::load_keypair(wallet_private_key)?;
        let wallet_pubkey = signer.pubkey();

        // Get the wallet's actual SOL balance
        let wallet_balance = self.rpc_client.get_balance(&wallet_pubkey).await
            .context("Failed to get wallet balance")?;

        // Create a temporary wallet config with the balance
        let wallet_config = crate::config::WalletConfig {
            private_key: wallet_private_key.to_string(),
            balance: Some(wallet_balance),
        };

        // Call the new method
        self.execute_swap_for_wallet_with_balance(wallet_config).await
    }

    /// Execute a swap for a single wallet using pre-fetched balance
    async fn execute_swap_for_wallet_with_balance(&self, wallet: crate::config::WalletConfig) -> Result<String> {
        // Load the keypair
        let signer = Self::load_keypair(&wallet.private_key)?;
        let wallet_pubkey = signer.pubkey();

        // Get token mints
        let input_mint = self.config.input_mint()?;
        let output_mint = self.config.output_mint()?;

        // Get the wallet balance from the wallet config
        let wallet_balance = wallet.balance.ok_or_else(||
            anyhow::anyhow!("Wallet balance not available"))?;

        // Check if wallet has enough balance to perform a swap (minimum 0.01 SOL)
        const MIN_REQUIRED_BALANCE: u64 = 10_000_000; // 0.01 SOL in lamports
        if wallet_balance < MIN_REQUIRED_BALANCE {
            return Err(anyhow::anyhow!("Wallet {} has insufficient balance: {} lamports (minimum required: {} lamports)",
                wallet_pubkey, wallet_balance, MIN_REQUIRED_BALANCE));
        }

        // Calculate a random amount between min_percentage and max_percentage of the actual balance
        // but ensure we leave some SOL for transaction fees (0.05 SOL)
        const RESERVE_FOR_FEES: u64 = 50_000_000; // 0.05 SOL in lamports
        let usable_balance = wallet_balance.saturating_sub(RESERVE_FOR_FEES);

        // Get min_percentage and max_percentage from config
        let min_percentage = self.config.swap.min_percentage;
        let max_percentage = self.config.swap.max_percentage;
        let percentage = min_percentage + (rand::random::<u8>() % (max_percentage - min_percentage + 1));
        let amount = usable_balance * percentage as u64 / 100;

        // Ensure the amount is greater than zero
        if amount == 0 {
            return Err(anyhow::anyhow!("Calculated swap amount is zero. Wallet {} has usable balance {} lamports",
                wallet_pubkey, usable_balance));
        }

        println!("Wallet {}: Total balance {} lamports, usable {} lamports, using {}%: {} lamports",
            wallet_pubkey, wallet_balance, usable_balance, percentage, amount);

        // Create quote request
        let quote_request = QuoteRequest {
            amount,
            input_mint,
            output_mint,
            slippage_bps: self.config.swap.slippage_bps,
            ..QuoteRequest::default()
        };

        // Get quote
        let quote_response = self.jupiter_client.quote(&quote_request).await
            .context("Failed to get quote")?;

        println!("Wallet {}: Quote received - In: {}, Out: {}",
            wallet_pubkey, quote_response.in_amount, quote_response.out_amount);

        // Create swap request
        let swap_response = self.jupiter_client
            .swap(
                &SwapRequest {
                    user_public_key: wallet_pubkey,
                    quote_response: quote_response.clone(),
                    config: TransactionConfig::default(),
                },
                None,
            )
            .await
            .context("Failed to create swap transaction")?;

        // Deserialize transaction
        let versioned_transaction: VersionedTransaction =
            bincode::deserialize(&swap_response.swap_transaction)
                .context("Failed to deserialize transaction")?;

        // Sign transaction
        let signed_versioned_transaction =
            VersionedTransaction::try_new(versioned_transaction.message, &[&signer])
                .context("Failed to sign transaction")?;

        // Send transaction
        let signature = self.rpc_client
            .send_and_confirm_transaction(&signed_versioned_transaction)
            .await
            .context("Failed to send transaction")?;

        Ok(signature.to_string())
    }

    /// Execute swaps for all wallets in parallel
    pub async fn execute_swaps(&self) -> Vec<Result<String>> {
        // Clone the config to get a mutable version
        let mut config = self.config.clone();

        // First, load all wallets and fetch their balances sequentially
        println!("Loading wallets and fetching balances...");
        for wallet in &mut config.wallets {
            if let Err(e) = self.load_wallet_with_balance(wallet).await {
                println!("Failed to load wallet: {}", e);
                // Set balance to None to indicate failure
                wallet.balance = None;
            }
        }

        let mut handles: Vec<JoinHandle<Result<String>>> = Vec::new();

        // Start a task for each wallet that has a valid balance
        for wallet in &config.wallets {
            // Skip wallets that failed to load or have no balance
            if wallet.balance.is_none() {
                continue;
            }

            let wallet_clone = wallet.clone();
            let bot = self.clone();

            let handle = tokio::spawn(async move {
                bot.execute_swap_for_wallet_with_balance(wallet_clone).await
            });

            handles.push(handle);
        }

        // Wait for all tasks to complete
        let mut results = Vec::new();
        for handle in handles {
            match handle.await {
                Ok(result) => results.push(result),
                Err(e) => results.push(Err(anyhow::anyhow!("Task panicked: {}", e))),
            }
        }

        results
    }
}

impl Clone for TradeBot {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jupiter_client: self.jupiter_client.clone(),
            rpc_client: Arc::clone(&self.rpc_client),
        }
    }
}
