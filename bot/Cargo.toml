[package]
name = "example"
version = "0.1.0"
description = ""
edition = { workspace = true }

[dependencies]
tokio = { version = "1", features = ["full"] }
jupiter-swap-api-client = { path = "../jupiter-swap-api-client" }
solana-sdk = { workspace = true }
solana-client = { workspace = true }
bincode = "1.3.3"
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rand = "0.8.5"