[package]
name = "jupiter-swap-api-client"
version = "0.1.0"
description = "Jupiter Swap API rust client"
edition = { workspace = true }

[dependencies]
anyhow = "1"
serde = { version = "1.0.159", features = ["derive"] }
serde_json = "1.0.95"
solana-sdk = { workspace = true }
solana-account-decoder = { workspace = true }
thiserror = "2"
base64 = "0.22.1"
reqwest = { version = "0.12", features = ["json"] }
rust_decimal = "1.36.0"
